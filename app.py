from fpdf import FPDF
import unicodedata

def clean_text_for_pdf(text):
    """Clean text by replacing problematic Unicode characters with ASCII equivalents"""
    # Replace smart quotes with regular quotes
    text = text.replace(''', "'").replace(''', "'").replace('"', '"').replace('"', '"')
    # Replace em dash and en dash with regular dash
    text = text.replace('—', '-').replace('–', '-')
    # Replace other problematic characters
    text = text.replace('…', '...')
    # Normalize Unicode characters to closest ASCII equivalent where possible
    text = unicodedata.normalize('NFKD', text)
    # Remove any remaining non-Latin-1 characters
    text = text.encode('latin-1', errors='ignore').decode('latin-1')
    return text

title = "254.000’de Hâlâ Güçlü: Bir Stilo Efsanesi"
subtitle = "Fiat Stilo 1.6 16V Manuel | Bordeaux Old Rose | 2. Sahibi – 2010’dan <PERSON>"

content = """
🎬 Giriş: Unutulan Bir Modelin Sessiz <PERSON>

2001 yılında Fiat, kompakt sınıfta Golf, Focus ve Astra gibi güçlü rakiplere karşı sahaya sürdü Stilo’yu. Kimisi için sadece bir ulaşım aracıydı. Ama bazıları için — seninki gibi — bu araba, yıllar geçse de yol tutuşuyla, ruhuyla ve karakteriyle özel kaldı.

Fiat Stilo’nun en sade ama en dengeli versiyonlarından biri: 1.6 16V manuel şanzımanlı versiyon. Ve bu özel model, Bordeaux Old Rose adı verilen nadir ve asil renkle birleştiğinde, artık sıradan bir otomobil değil, zamana direnen bir karaktere dönüşüyor.

🧰 Teknik Temeller: 1.6 16V'nin Altın Oranı

- Motor: 1.6 litre 16 supaplı, atmosferik benzinli
- Güç: 103 beygir
- 0-100 km/s: Resmi verilere göre 11.2 sn, fakat bu özel araçla yapılan testte 10.7 saniye ölçülmüş!
- Şanzıman: 5 ileri manuel
- Şasi: Yol tutuşta iddialı, konforla dengeli
- KM Durumu: 254.600 km ve motor hiç açılmamış 🏁

🛣️ Yollarda Geçen 14+ Yıl

2010 yılında ikinci sahibi tarafından alınan bu Stilo, Türkiye yollarında geçen 14 yılı geride bıraktı.
Bu süreçte:
- Motoru açılmadı,
- Bakımları zamanında yapıldı,
- Elektriksel veya kronik sorunları minimumda tutuldu,
- Ve en önemlisi: Araba, asla “sadece bir araba” gibi kullanılmadı.

Sürücüsü, onu bir görev aracı değil, karakteri olan bir yol arkadaşı gibi gördü.
Zaten böyle bir sürücü olmasaydı, 254.000 kilometrede hâlâ fabrika performansına yaklaşmak mümkün olmazdı.

🔍 10.7 Saniye – Yaşla Değil, Bakımla Ölçülür

Bu Stilo, performans denemesinde 10.7 saniyelik 0-100 km/s süresi yakalamış durumda.
Bu da gösteriyor ki:
- Silindir içi kompresyon hâlâ güçlü
- Ateşleme sistemi dengeli
- Şanzıman, debriyaj ve ECU hâlâ senkron çalışıyor
- Sürücü, aracını tanıyor, hissediyor

🎨 Bordeaux Old Rose: Rengin Duruşu

Bugün piyasada kaç Stilo kaldı bu renkte? Çok az.
Bu renk, ışığa göre kırmızıdan kahverengiye, bordo alt tonlara dönüşen metallic özel boya sınıfına giriyor.
Yıllar içinde solmaması için özen gerektiriyor. Bu da gösteriyor ki, bu araç sadece mekanik olarak değil, kozmetik olarak da gözetilmiş.

🛠️ Bugün Ne Durumda?

- Süspansiyonlar hâlâ sağlam mı? → Evet
- Vites geçişleri yumuşak mı? → Evet
- Direksiyon boşluğu yok denecek kadar az
- Rölanti kararlı, motor sesi dengeli
- Yakıt tüketimi makul, yağ eksiltme yok

🧡 Son Söz: Bu Bir Sıradanlık Değil, Tutku İşidir

Fiat Stilo, birçok kişinin gözünden kaçtı belki. Ama onun değerini bilenler, onunla kilometrelerce yol yapanlar için bu araç hâlâ çok şey ifade ediyor.
Senin Stilo’n da bunlardan biri.
254.000 km’de hâlâ gürül gürül çalışan bir motor, zamana karşı koyan bir gövde ve sadık bir kullanıcı.
Belki de Stilo’nun gerçek efsaneleri kataloglarda değil, senin gibi kullanıcıların garajlarında yaşıyor.

📝 İmzalı:
Bir Stilo Sahibi, Ama Sıradan Değil
2010’dan Beri Yolda, Keyifle, Hakkını Vererek...
"""

# PDF oluştur
pdf = FPDF()
pdf.add_page()
pdf.set_auto_page_break(auto=True, margin=15)
pdf.set_font("Arial", 'B', 16)
pdf.cell(0, 10, clean_text_for_pdf(title), ln=True)
pdf.set_font("Arial", '', 12)
pdf.cell(0, 10, clean_text_for_pdf(subtitle), ln=True)
pdf.ln(10)
pdf.set_font("Arial", '', 11)
for line in content.strip().split("\n"):
    cleaned_line = clean_text_for_pdf(line.strip())
    if cleaned_line:  # Only add non-empty lines
        pdf.multi_cell(0, 8, cleaned_line)
pdf.output("fiat_stilo_efsanesi.pdf")
